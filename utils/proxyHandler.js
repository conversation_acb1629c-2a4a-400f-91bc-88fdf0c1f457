const { createProxyMiddleware } = require('http-proxy-middleware');
const configManager = require('./configManager');

class ProxyHandler {
  constructor() {
    this.currentProxy = null;
    this.proxyMiddleware = null;
  }

  /**
   * 创建代理中间件
   */
  async createProxy() {
    const activeProxy = await configManager.getActiveProxy();
    
    if (!activeProxy) {
      return null;
    }

    // 如果当前代理没有变化，返回现有的中间件
    if (this.currentProxy && this.currentProxy.id === activeProxy.id) {
      return this.proxyMiddleware;
    }

    // 创建新的代理中间件
    this.currentProxy = activeProxy;
    
    // 配置路径重写规则
    const pathRewriteRules = {
      '^/proxy': '', // 移除 /proxy 前缀
    };
    
    // 如果配置了忽略路径，则重写所有路径为目标URL
    if (activeProxy.ignorePath) {
      pathRewriteRules['^.*$'] = '';
    }
    
    this.proxyMiddleware = createProxyMiddleware({
      target: activeProxy.url,
      changeOrigin: true,
      pathRewrite: pathRewriteRules,
      timeout: activeProxy.timeout,
      proxyTimeout: activeProxy.timeout,
      onError: (err, req, res) => {
        console.error(`代理错误 [${activeProxy.name}]:`, err.message);
        res.status(502).json({
          error: '代理服务器错误',
          message: err.message,
          proxy: activeProxy.name
        });
      },
      onProxyReq: (proxyReq, req, res) => {
        // 记录代理请求
        console.log(`代理请求 [${activeProxy.name}]: ${req.method} ${req.url}`);
        
        // 确保请求头正确传递
        if (req.body && (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH')) {
          const bodyData = JSON.stringify(req.body);
          proxyReq.setHeader('Content-Type', 'application/json');
          proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
          proxyReq.write(bodyData);
        }
      },
      onProxyRes: (proxyRes, req, res) => {
        // 记录代理响应
        console.log(`代理响应 [${activeProxy.name}]: ${proxyRes.statusCode} ${req.method} ${req.url}`);
        
        // 添加自定义响应头
        proxyRes.headers['X-Proxy-By'] = 'HaoProxy';
        proxyRes.headers['X-Proxy-Target'] = activeProxy.name;
      },
      logLevel: 'warn'
    });

    return this.proxyMiddleware;
  }

  /**
   * 获取当前激活的代理信息
   */
  getCurrentProxy() {
    return this.currentProxy;
  }

  /**
   * 重置代理中间件
   */
  resetProxy() {
    this.currentProxy = null;
    this.proxyMiddleware = null;
  }

  /**
   * 检查是否有激活的代理
   */
  async hasActiveProxy() {
    const activeProxy = await configManager.getActiveProxy();
    return !!activeProxy;
  }

  /**
   * 动态代理中间件函数
   */
  async dynamicProxy(req, res, next) {
    try {
      const proxyMiddleware = await this.createProxy();
      
      if (!proxyMiddleware) {
        return res.status(503).json({
          error: '没有可用的代理服务',
          message: '请先启用一个代理接口'
        });
      }

      // 执行代理
      proxyMiddleware(req, res, next);
    } catch (error) {
      console.error('动态代理错误:', error);
      res.status(500).json({
        error: '代理配置错误',
        message: error.message
      });
    }
  }
}

module.exports = new ProxyHandler();
