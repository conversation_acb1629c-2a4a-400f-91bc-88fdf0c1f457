const { createProxyMiddleware } = require('http-proxy-middleware');
const axios = require('axios');
const configManager = require('./configManager');
const { logger } = require('../middleware/logger');

class ProxyHandler {
  constructor() {
    this.currentProxy = null;
    this.proxyMiddleware = null;
  }

  /**
   * 创建代理中间件
   */
  async createProxy() {
    const activeProxy = await configManager.getActiveProxy();
    
    if (!activeProxy) {
      return null;
    }

    // 如果当前代理没有变化，返回现有的中间件
    if (this.currentProxy && this.currentProxy.id === activeProxy.id) {
      return this.proxyMiddleware;
    }

    // 创建新的代理中间件
    this.currentProxy = activeProxy;
    
    // 配置路径重写规则
    const pathRewriteRules = {
      '^/proxy': '', // 移除 /proxy 前缀
    };
    
    // 如果配置了忽略路径，则重写所有路径为目标URL
    if (activeProxy.ignorePath) {
      pathRewriteRules['^.*$'] = '';
    }
    
    this.proxyMiddleware = createProxyMiddleware({
      target: activeProxy.url,
      changeOrigin: true,
      pathRewrite: pathRewriteRules,
      timeout: activeProxy.timeout,
      proxyTimeout: activeProxy.timeout,
      onError: (err, req, res) => {
        console.error(`代理错误 [${activeProxy.name}]:`, err.message);
        res.status(502).json({
          error: '代理服务器错误',
          message: err.message,
          proxy: activeProxy.name
        });
      },
      onProxyReq: (proxyReq, req, res) => {
        // 记录代理请求
        console.log(`代理请求 [${activeProxy.name}]: ${req.method} ${req.url}`);
        
        // 确保请求头正确传递
        if (req.body && (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH')) {
          const bodyData = JSON.stringify(req.body);
          proxyReq.setHeader('Content-Type', 'application/json');
          proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
          proxyReq.write(bodyData);
        }
      },
      onProxyRes: (proxyRes, req, res) => {
        // 记录代理响应
        console.log(`代理响应 [${activeProxy.name}]: ${proxyRes.statusCode} ${req.method} ${req.url}`);
        
        // 添加自定义响应头
        proxyRes.headers['X-Proxy-By'] = 'HaoProxy';
        proxyRes.headers['X-Proxy-Target'] = activeProxy.name;
      },
      logLevel: 'warn'
    });

    return this.proxyMiddleware;
  }

  /**
   * 获取当前激活的代理信息
   */
  getCurrentProxy() {
    return this.currentProxy;
  }

  /**
   * 重置代理中间件
   */
  resetProxy() {
    this.currentProxy = null;
    this.proxyMiddleware = null;
  }

  /**
   * 检查是否有激活的代理
   */
  async hasActiveProxy() {
    const activeProxy = await configManager.getActiveProxy();
    return !!activeProxy;
  }

  /**
   * API请求转换代理函数
   */
  async transformAndProxy(req, res, next) {
    try {
      const activeProxy = await configManager.getActiveProxy();

      if (!activeProxy) {
        return res.status(503).json({
          error: '没有可用的代理服务',
          message: '请先启用一个代理接口'
        });
      }

      // 构建目标URL
      let targetUrl = activeProxy.url;
      if (!targetUrl.endsWith('/')) {
        targetUrl += '/';
      }

      // 如果不忽略路径，则保留原始路径（去除/proxy前缀）
      if (!activeProxy.ignorePath) {
        const originalPath = req.path.replace(/^\/proxy/, '');
        if (originalPath && originalPath !== '/') {
          targetUrl += originalPath.startsWith('/') ? originalPath.slice(1) : originalPath;
        }
      }

      // 添加查询参数
      if (req.query && Object.keys(req.query).length > 0) {
        const queryString = new URLSearchParams(req.query).toString();
        targetUrl += (targetUrl.includes('?') ? '&' : '?') + queryString;
      }

      // 准备请求头
      const headers = { ...req.headers };

      // 删除可能导致问题的头部
      delete headers.host;
      delete headers['content-length'];

      // 替换Authorization头中的API Key
      if (activeProxy.apiKey) {
        headers['Authorization'] = `Bearer ${activeProxy.apiKey}`;
      }

      // 准备请求体
      let requestBody = req.body;

      // 如果是JSON请求且配置了模型替换，则替换model字段
      if (requestBody && typeof requestBody === 'object' && activeProxy.model) {
        requestBody = { ...requestBody };
        if (requestBody.model !== undefined) {
          logger.info(`模型替换: ${requestBody.model} -> ${activeProxy.model}`);
          requestBody.model = activeProxy.model;
        }
      }

      logger.info(`代理请求转换 [${activeProxy.name}]: ${req.method} ${req.originalUrl} -> ${targetUrl}`);

      // 发送转换后的请求
      const axiosConfig = {
        method: req.method.toLowerCase(),
        url: targetUrl,
        headers: headers,
        timeout: activeProxy.timeout || 30000,
        validateStatus: () => true, // 接受所有状态码
      };

      // 只有在有请求体的情况下才添加data
      if (requestBody && ['post', 'put', 'patch'].includes(axiosConfig.method)) {
        axiosConfig.data = requestBody;
      }

      const response = await axios(axiosConfig);

      // 设置响应头
      Object.keys(response.headers).forEach(key => {
        // 跳过一些可能导致问题的头部
        if (!['content-encoding', 'transfer-encoding', 'connection'].includes(key.toLowerCase())) {
          res.set(key, response.headers[key]);
        }
      });

      // 添加自定义响应头
      res.set('X-Proxy-By', 'HaoProxy');
      res.set('X-Proxy-Target', activeProxy.name);

      // 设置状态码并返回响应
      res.status(response.status);

      if (response.data) {
        res.json(response.data);
      } else {
        res.end();
      }

      logger.info(`代理响应 [${activeProxy.name}]: ${response.status} ${req.method} ${req.originalUrl}`);

    } catch (error) {
      logger.error('API请求转换代理错误:', error);

      if (error.code === 'ECONNREFUSED') {
        res.status(502).json({
          error: '目标服务器连接失败',
          message: '无法连接到目标API服务器'
        });
      } else if (error.code === 'ETIMEDOUT') {
        res.status(504).json({
          error: '请求超时',
          message: '目标API服务器响应超时'
        });
      } else {
        res.status(500).json({
          error: '代理转换错误',
          message: error.message
        });
      }
    }
  }

  /**
   * 动态代理中间件函数（保持向后兼容）
   */
  async dynamicProxy(req, res, next) {
    // 使用新的转换代理函数
    return this.transformAndProxy(req, res, next);
  }
}

module.exports = new ProxyHandler();
