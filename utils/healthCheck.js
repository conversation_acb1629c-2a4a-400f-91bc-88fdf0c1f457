const https = require('https');
const http = require('http');
const { URL } = require('url');
const { logger } = require('../middleware/logger');

class HealthChecker {
  constructor() {
    this.healthStatus = new Map();
    this.checkInterval = null;
  }

  /**
   * 检查单个代理的健康状态
   */
  async checkProxyHealth(proxy) {
    return new Promise((resolve) => {
      try {
        const url = new URL(proxy.url);
        const isHttps = url.protocol === 'https:';
        const client = isHttps ? https : http;
        
        const options = {
          hostname: url.hostname,
          port: url.port || (isHttps ? 443 : 80),
          path: '/health', // 尝试访问健康检查端点
          method: 'GET',
          timeout: 5000,
          headers: {
            'User-Agent': 'HaoProxy-HealthChecker/1.0'
          }
        };

        const req = client.request(options, (res) => {
          const healthData = {
            id: proxy.id,
            name: proxy.name,
            url: proxy.url,
            status: 'healthy',
            statusCode: res.statusCode,
            responseTime: Date.now() - startTime,
            lastCheck: new Date().toISOString(),
            message: `HTTP ${res.statusCode}`
          };

          // 如果状态码不是2xx，标记为不健康
          if (res.statusCode < 200 || res.statusCode >= 300) {
            healthData.status = 'unhealthy';
            healthData.message = `HTTP ${res.statusCode} - 服务器响应异常`;
          }

          this.healthStatus.set(proxy.id, healthData);
          resolve(healthData);
        });

        const startTime = Date.now();

        req.on('error', (error) => {
          const healthData = {
            id: proxy.id,
            name: proxy.name,
            url: proxy.url,
            status: 'unhealthy',
            statusCode: null,
            responseTime: Date.now() - startTime,
            lastCheck: new Date().toISOString(),
            message: error.message,
            error: error.code || 'CONNECTION_ERROR'
          };

          this.healthStatus.set(proxy.id, healthData);
          resolve(healthData);
        });

        req.on('timeout', () => {
          req.destroy();
          const healthData = {
            id: proxy.id,
            name: proxy.name,
            url: proxy.url,
            status: 'unhealthy',
            statusCode: null,
            responseTime: 5000,
            lastCheck: new Date().toISOString(),
            message: '连接超时',
            error: 'TIMEOUT'
          };

          this.healthStatus.set(proxy.id, healthData);
          resolve(healthData);
        });

        req.end();
      } catch (error) {
        const healthData = {
          id: proxy.id,
          name: proxy.name,
          url: proxy.url,
          status: 'unhealthy',
          statusCode: null,
          responseTime: 0,
          lastCheck: new Date().toISOString(),
          message: error.message,
          error: 'INVALID_URL'
        };

        this.healthStatus.set(proxy.id, healthData);
        resolve(healthData);
      }
    });
  }

  /**
   * 检查所有代理的健康状态
   */
  async checkAllProxies(proxies) {
    const promises = proxies.map(proxy => this.checkProxyHealth(proxy));
    const results = await Promise.all(promises);
    
    logger.info('健康检查完成', {
      total: results.length,
      healthy: results.filter(r => r.status === 'healthy').length,
      unhealthy: results.filter(r => r.status === 'unhealthy').length
    });

    return results;
  }

  /**
   * 获取代理健康状态
   */
  getProxyHealth(proxyId) {
    return this.healthStatus.get(proxyId) || {
      id: proxyId,
      status: 'unknown',
      message: '尚未检查',
      lastCheck: null
    };
  }

  /**
   * 获取所有代理健康状态
   */
  getAllHealthStatus() {
    return Array.from(this.healthStatus.values());
  }

  /**
   * 启动定期健康检查
   */
  startPeriodicCheck(proxies, interval = 300000) { // 默认5分钟
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(async () => {
      try {
        await this.checkAllProxies(proxies);
      } catch (error) {
        logger.error('定期健康检查失败:', error);
      }
    }, interval);

    logger.info(`启动定期健康检查，间隔: ${interval / 1000}秒`);
  }

  /**
   * 停止定期健康检查
   */
  stopPeriodicCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      logger.info('停止定期健康检查');
    }
  }

  /**
   * 清除健康状态缓存
   */
  clearHealthStatus() {
    this.healthStatus.clear();
  }
}

module.exports = new HealthChecker();
