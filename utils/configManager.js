const fs = require('fs').promises;
const path = require('path');

class ConfigManager {
  constructor() {
    this.configPath = path.join(__dirname, '../config/proxies.json');
    this.config = null;
  }

  /**
   * 加载配置文件
   */
  async loadConfig() {
    try {
      const data = await fs.readFile(this.configPath, 'utf8');
      this.config = JSON.parse(data);
      return this.config;
    } catch (error) {
      console.error('加载配置文件失败:', error);
      throw new Error('配置文件加载失败');
    }
  }

  /**
   * 保存配置文件
   */
  async saveConfig() {
    try {
      await fs.writeFile(this.configPath, JSON.stringify(this.config, null, 2), 'utf8');
      return true;
    } catch (error) {
      console.error('保存配置文件失败:', error);
      throw new Error('配置文件保存失败');
    }
  }

  /**
   * 获取所有代理配置
   */
  async getProxies() {
    if (!this.config) {
      await this.loadConfig();
    }
    return this.config.proxies;
  }

  /**
   * 获取当前激活的代理
   */
  async getActiveProxy() {
    const proxies = await this.getProxies();
    return proxies.find(proxy => proxy.enabled);
  }

  /**
   * 根据ID获取代理配置
   */
  async getProxyById(id) {
    const proxies = await this.getProxies();
    return proxies.find(proxy => proxy.id === id);
  }

  /**
   * 切换代理状态（确保互斥性）
   */
  async toggleProxy(id) {
    if (!this.config) {
      await this.loadConfig();
    }

    // 首先关闭所有代理
    this.config.proxies.forEach(proxy => {
      proxy.enabled = false;
    });

    // 启用指定的代理
    const targetProxy = this.config.proxies.find(proxy => proxy.id === id);
    if (!targetProxy) {
      throw new Error(`代理 ${id} 不存在`);
    }

    targetProxy.enabled = true;
    await this.saveConfig();
    return targetProxy;
  }

  /**
   * 添加新的代理配置
   */
  async addProxy(proxyConfig) {
    if (!this.config) {
      await this.loadConfig();
    }

    // 验证必要字段
    if (!proxyConfig.id || !proxyConfig.name || !proxyConfig.url) {
      throw new Error('代理配置缺少必要字段');
    }

    // 检查ID是否已存在
    if (this.config.proxies.find(proxy => proxy.id === proxyConfig.id)) {
      throw new Error(`代理ID ${proxyConfig.id} 已存在`);
    }

    // 设置默认值
    const newProxy = {
      id: proxyConfig.id,
      name: proxyConfig.name,
      url: proxyConfig.url,
      enabled: false,
      timeout: proxyConfig.timeout || 30000,
      description: proxyConfig.description || '',
      apiKey: proxyConfig.apiKey || '',
      model: proxyConfig.model || ''
    };

    this.config.proxies.push(newProxy);
    await this.saveConfig();
    return newProxy;
  }

  /**
   * 删除代理配置
   */
  async deleteProxy(id) {
    if (!this.config) {
      await this.loadConfig();
    }

    const index = this.config.proxies.findIndex(proxy => proxy.id === id);
    if (index === -1) {
      throw new Error(`代理 ${id} 不存在`);
    }

    const deletedProxy = this.config.proxies.splice(index, 1)[0];
    await this.saveConfig();
    return deletedProxy;
  }

  /**
   * 更新代理配置
   */
  async updateProxy(id, updates) {
    if (!this.config) {
      await this.loadConfig();
    }

    const proxy = this.config.proxies.find(proxy => proxy.id === id);
    if (!proxy) {
      throw new Error(`代理 ${id} 不存在`);
    }

    // 更新允许的字段
    const allowedFields = ['name', 'url', 'timeout', 'description', 'apiKey', 'model'];
    allowedFields.forEach(field => {
      if (updates[field] !== undefined) {
        proxy[field] = updates[field];
      }
    });

    await this.saveConfig();
    return proxy;
  }

  /**
   * 获取系统设置
   */
  async getSettings() {
    if (!this.config) {
      await this.loadConfig();
    }
    return this.config.settings;
  }

  /**
   * 更新系统设置
   */
  async updateSettings(settings) {
    if (!this.config) {
      await this.loadConfig();
    }

    this.config.settings = { ...this.config.settings, ...settings };
    await this.saveConfig();
    return this.config.settings;
  }
}

module.exports = new ConfigManager();
