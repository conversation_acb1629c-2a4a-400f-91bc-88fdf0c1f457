{"proxies": [{"id": "openai-api", "name": "Qwen3-Coder-480B-A35B-Instruct", "url": "https://api-inference.modelscope.cn/v1/", "enabled": true, "ignorePath": true, "timeout": 30000, "description": "Qwen3-Coder-480B-A35B-Instruct", "apiKey": "ms-58bf6d9d-a9ab-47af-8433-91fbf1bbd2cc", "model": "Qwen/Qwen3-Coder-480B-A35B-Instruct"}, {"id": "gemini-api", "name": "Gemini API", "url": "https://generativelanguage.googleapis.com", "enabled": false, "timeout": 30000, "description": "Google Gemini API接口", "apiKey": "", "model": "gemini-pro"}], "settings": {"port": 3000, "maxRequestSize": "100mb", "healthCheckInterval": 300000, "logLevel": "info"}}