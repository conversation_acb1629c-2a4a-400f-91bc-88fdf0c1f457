// HaoProxy 前端应用逻辑
function proxyManager() {
    return {
        // 数据状态
        proxies: [],
        healthStatus: [],
        activeProxy: null,
        loading: false,
        
        // UI状态
        showAddModal: false,
        editingProxy: null,
        notification: {
            show: false,
            message: '',
            type: 'success'
        },

        // 新代理表单数据
        newProxy: {
            id: '',
            name: '',
            url: '',
            description: '',
            timeout: 30000,
            apiKey: '',
            model: ''
        },

        // 初始化
        async init() {
            await this.refreshData();
            this.startAutoRefresh();
        },

        // 刷新所有数据
        async refreshData() {
            this.loading = true;
            try {
                await Promise.all([
                    this.loadProxies(),
                    this.loadHealthStatus()
                ]);
            } catch (error) {
                this.showNotification('数据加载失败: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },

        // 加载代理列表
        async loadProxies() {
            try {
                const response = await fetch('/api/proxies');
                const result = await response.json();
                
                if (result.success) {
                    this.proxies = result.data.proxies;
                    this.activeProxy = this.proxies.find(p => p.enabled) || null;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('加载代理列表失败:', error);
                throw error;
            }
        },

        // 加载健康状态
        async loadHealthStatus() {
            try {
                const response = await fetch('/api/health');
                const result = await response.json();
                
                if (result.success) {
                    this.healthStatus = result.data.health;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('加载健康状态失败:', error);
                // 健康状态加载失败不阻断主流程
            }
        },

        // 切换代理状态
        async toggleProxy(proxyId) {
            this.loading = true;
            try {
                const response = await fetch(`/api/proxies/${proxyId}/toggle`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    await this.loadProxies();
                    this.showNotification(result.message, 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showNotification('切换代理失败: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },

        // 测试连接
        async testConnection(proxyId) {
            this.loading = true;
            try {
                const response = await fetch(`/api/proxies/${proxyId}/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    await this.loadHealthStatus();
                    const health = result.data.health;
                    const message = `连接测试完成: ${health.status === 'healthy' ? '健康' : '异常'} (${health.responseTime}ms)`;
                    this.showNotification(message, health.status === 'healthy' ? 'success' : 'error');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showNotification('连接测试失败: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },

        // 删除代理
        async deleteProxy(proxyId) {
            if (!confirm('确定要删除这个代理吗？')) {
                return;
            }

            this.loading = true;
            try {
                const response = await fetch(`/api/proxies/${proxyId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    await this.loadProxies();
                    this.showNotification(result.message, 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showNotification('删除代理失败: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },

        // 添加新代理
        async addProxy() {
            this.loading = true;
            try {
                const response = await fetch('/api/proxies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.newProxy)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    await this.loadProxies();
                    this.closeModal();
                    this.showNotification(result.message, 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showNotification('添加代理失败: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },

        // 检查所有代理健康状态
        async checkAllHealth() {
            this.loading = true;
            try {
                const response = await fetch('/api/health/check', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.healthStatus = result.data.health;
                    this.showNotification('健康检查完成', 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showNotification('健康检查失败: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },

        // 编辑代理
        editProxy(proxy) {
            this.editingProxy = proxy;
            this.newProxy = {
                id: proxy.id,
                name: proxy.name,
                url: proxy.url,
                description: proxy.description || '',
                timeout: proxy.timeout || 30000,
                apiKey: proxy.apiKey || '',
                model: proxy.model || ''
            };
            this.showAddModal = true;
        },

        // 更新代理
        async updateProxy() {
            this.loading = true;
            try {
                const response = await fetch(`/api/proxies/${this.editingProxy.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.newProxy)
                });

                const result = await response.json();

                if (result.success) {
                    await this.loadProxies();
                    this.closeModal();
                    this.showNotification(result.message, 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showNotification('更新代理失败: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },

        // 关闭模态框
        closeModal() {
            this.showAddModal = false;
            this.editingProxy = null;
            this.resetNewProxy();
        },

        // 重置新代理表单
        resetNewProxy() {
            this.newProxy = {
                id: '',
                name: '',
                url: '',
                description: '',
                timeout: 30000,
                apiKey: '',
                model: ''
            };
        },

        // 显示通知
        showNotification(message, type = 'success') {
            this.notification = {
                show: true,
                message: message,
                type: type
            };
            
            // 3秒后自动隐藏
            setTimeout(() => {
                this.notification.show = false;
            }, 3000);
        },

        // 获取健康状态样式类
        getHealthStatusClass(proxyId) {
            const health = this.healthStatus.find(h => h.id === proxyId);
            if (!health) return 'bg-gray-400';
            
            switch (health.status) {
                case 'healthy':
                    return 'bg-green-500';
                case 'unhealthy':
                    return 'bg-red-500';
                default:
                    return 'bg-gray-400';
            }
        },

        // 获取健康状态文本
        getHealthStatusText(proxyId) {
            const health = this.healthStatus.find(h => h.id === proxyId);
            if (!health) return '未知';
            
            switch (health.status) {
                case 'healthy':
                    return `健康 (${health.responseTime}ms)`;
                case 'unhealthy':
                    return `异常: ${health.message}`;
                default:
                    return '未检查';
            }
        },

        // 启动自动刷新
        startAutoRefresh() {
            // 每30秒自动刷新健康状态
            setInterval(async () => {
                if (!this.loading) {
                    try {
                        await this.loadHealthStatus();
                    } catch (error) {
                        console.error('自动刷新健康状态失败:', error);
                    }
                }
            }, 30000);
        },

        // 计算属性
        get healthyCount() {
            return this.healthStatus.filter(h => h.status === 'healthy').length;
        },

        get unhealthyCount() {
            return this.healthStatus.filter(h => h.status === 'unhealthy').length;
        }
    };
}
