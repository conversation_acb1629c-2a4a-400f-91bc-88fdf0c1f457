const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');

// 导入中间件
const { requestLogger, logger } = require('./middleware/logger');
const { 
  notFound<PERSON>and<PERSON>, 
  errorHandler, 
  handlePayloadTooLarge 
} = require('./middleware/errorHandler');

// 导入路由
const apiRoutes = require('./routes/api');
const proxyRoutes = require('./routes/proxy');

// 导入配置管理器
const configManager = require('./utils/configManager');

class HaoProxyServer {
  constructor() {
    this.app = express();
    this.server = null;
    this.port = 3000;
  }

  /**
   * 初始化服务器配置
   */
  async initialize() {
    try {
      // 加载配置
      const settings = await configManager.getSettings();
      this.port = settings.port || 3000;

      // 配置中间件
      this.setupMiddleware();
      
      // 配置路由
      this.setupRoutes();
      
      // 配置错误处理
      this.setupErrorHandling();

      logger.info('服务器初始化完成');
    } catch (error) {
      logger.error('服务器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置中间件
   */
  setupMiddleware() {
    // 安全中间件
    this.app.use(helmet({
      contentSecurityPolicy: false, // 允许内联脚本用于Alpine.js
    }));

    // CORS配置
    this.app.use(cors({
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // 请求体解析
    this.app.use(express.json({ limit: '100mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '100mb' }));

    // 请求大小限制错误处理
    this.app.use(handlePayloadTooLarge);

    // 请求日志
    this.app.use(requestLogger);

    // 静态文件服务
    this.app.use(express.static(path.join(__dirname, 'public')));

    // 信任代理
    this.app.set('trust proxy', true);
  }

  /**
   * 设置路由
   */
  setupRoutes() {
    // 健康检查端点
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: require('./package.json').version
      });
    });

    // 主页路由
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });

    // API路由
    this.app.use('/api', apiRoutes);

    // 代理路由
    this.app.use('/proxy', proxyRoutes);
  }

  /**
   * 设置错误处理
   */
  setupErrorHandling() {
    // 404处理
    this.app.use(notFoundHandler);

    // 全局错误处理
    this.app.use(errorHandler);
  }

  /**
   * 启动服务器
   */
  async start() {
    try {
      await this.initialize();

      this.server = this.app.listen(this.port, () => {
        logger.info(`HaoProxy服务器启动成功`);
        logger.info(`服务器地址: http://localhost:${this.port}`);
        logger.info(`管理界面: http://localhost:${this.port}`);
        logger.info(`API文档: http://localhost:${this.port}/api`);
        logger.info(`代理端点: http://localhost:${this.port}/proxy/*`);
      });

      // 优雅关闭处理
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('服务器启动失败:', error);
      process.exit(1);
    }
  }

  /**
   * 设置优雅关闭
   */
  setupGracefulShutdown() {
    const shutdown = (signal) => {
      logger.info(`收到 ${signal} 信号，开始优雅关闭...`);
      
      if (this.server) {
        this.server.close((err) => {
          if (err) {
            logger.error('服务器关闭错误:', err);
            process.exit(1);
          }
          
          logger.info('服务器已优雅关闭');
          process.exit(0);
        });

        // 强制关闭超时
        setTimeout(() => {
          logger.error('强制关闭服务器');
          process.exit(1);
        }, 10000);
      } else {
        process.exit(0);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
  }

  /**
   * 停止服务器
   */
  async stop() {
    return new Promise((resolve, reject) => {
      if (this.server) {
        this.server.close((err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }
}

// 创建并启动服务器实例
const server = new HaoProxyServer();

// 如果直接运行此文件，启动服务器
if (require.main === module) {
  server.start().catch(error => {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  });
}

module.exports = server;
