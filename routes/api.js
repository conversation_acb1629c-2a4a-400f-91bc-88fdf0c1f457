const express = require('express');
const router = express.Router();

// 导入工具和中间件
const configManager = require('../utils/configManager');
const proxyHandler = require('../utils/proxyHandler');
const healthChecker = require('../utils/healthCheck');
const { asyncHandler, validateRequestBody } = require('../middleware/errorHandler');
const { logger } = require('../middleware/logger');

/**
 * API根路径 - 返回API信息
 */
router.get('/', (req, res) => {
  res.json({
    name: 'HaoProxy API',
    version: '1.0.0',
    description: 'AI接口代理管理系统API',
    endpoints: {
      'GET /api/proxies': '获取所有代理配置',
      'PUT /api/proxies/:id/toggle': '切换代理状态',
      'POST /api/proxies': '添加新代理配置',
      'DELETE /api/proxies/:id': '删除代理配置',
      'PUT /api/proxies/:id': '更新代理配置',
      'POST /api/proxies/:id/test': '测试代理连接',
      'GET /api/health': '获取所有代理健康状态',
      'POST /api/health/check': '手动触发健康检查',
      'GET /api/status': '获取系统状态'
    }
  });
});

/**
 * 获取所有代理配置
 * GET /api/proxies
 */
router.get('/proxies', asyncHandler(async (req, res) => {
  const proxies = await configManager.getProxies();
  const activeProxy = await configManager.getActiveProxy();
  
  res.json({
    success: true,
    data: {
      proxies: proxies,
      activeProxy: activeProxy ? activeProxy.id : null,
      total: proxies.length
    }
  });
}));

/**
 * 切换代理状态
 * PUT /api/proxies/:id/toggle
 */
router.put('/proxies/:id/toggle', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  try {
    const updatedProxy = await configManager.toggleProxy(id);
    
    // 重置代理处理器以应用新配置
    proxyHandler.resetProxy();
    
    logger.info(`代理切换成功: ${updatedProxy.name} (${id})`);
    
    res.json({
      success: true,
      message: `代理 "${updatedProxy.name}" 已激活`,
      data: {
        activeProxy: updatedProxy
      }
    });
  } catch (error) {
    logger.error(`代理切换失败: ${id}`, error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
}));

/**
 * 添加新代理配置
 * POST /api/proxies
 */
router.post('/proxies', 
  validateRequestBody(['id', 'name', 'url']),
  asyncHandler(async (req, res) => {
    try {
      const newProxy = await configManager.addProxy(req.body);
      
      logger.info(`新代理添加成功: ${newProxy.name} (${newProxy.id})`);
      
      res.status(201).json({
        success: true,
        message: `代理 "${newProxy.name}" 添加成功`,
        data: {
          proxy: newProxy
        }
      });
    } catch (error) {
      logger.error('代理添加失败', error);
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  })
);

/**
 * 删除代理配置
 * DELETE /api/proxies/:id
 */
router.delete('/proxies/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  try {
    const deletedProxy = await configManager.deleteProxy(id);
    
    // 如果删除的是当前激活的代理，重置代理处理器
    const currentProxy = proxyHandler.getCurrentProxy();
    if (currentProxy && currentProxy.id === id) {
      proxyHandler.resetProxy();
    }
    
    logger.info(`代理删除成功: ${deletedProxy.name} (${id})`);
    
    res.json({
      success: true,
      message: `代理 "${deletedProxy.name}" 删除成功`,
      data: {
        deletedProxy: deletedProxy
      }
    });
  } catch (error) {
    logger.error(`代理删除失败: ${id}`, error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
}));

/**
 * 测试代理连接
 * POST /api/proxies/:id/test
 */
router.post('/proxies/:id/test', asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const proxy = await configManager.getProxyById(id);
    if (!proxy) {
      return res.status(404).json({
        success: false,
        message: `代理 ${id} 不存在`
      });
    }

    const healthResult = await healthChecker.checkProxyHealth(proxy);

    res.json({
      success: true,
      message: `代理 "${proxy.name}" 连接测试完成`,
      data: {
        health: healthResult
      }
    });
  } catch (error) {
    logger.error(`代理连接测试失败: ${id}`, error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
}));

/**
 * 获取所有代理健康状态
 * GET /api/health
 */
router.get('/health', asyncHandler(async (req, res) => {
  const healthStatus = healthChecker.getAllHealthStatus();
  const proxies = await configManager.getProxies();

  // 确保所有代理都有健康状态信息
  const completeHealthStatus = proxies.map(proxy => {
    const health = healthChecker.getProxyHealth(proxy.id);
    return {
      ...health,
      enabled: proxy.enabled
    };
  });

  res.json({
    success: true,
    data: {
      health: completeHealthStatus,
      summary: {
        total: proxies.length,
        healthy: completeHealthStatus.filter(h => h.status === 'healthy').length,
        unhealthy: completeHealthStatus.filter(h => h.status === 'unhealthy').length,
        unknown: completeHealthStatus.filter(h => h.status === 'unknown').length
      }
    }
  });
}));

/**
 * 手动触发健康检查
 * POST /api/health/check
 */
router.post('/health/check', asyncHandler(async (req, res) => {
  try {
    const proxies = await configManager.getProxies();
    const healthResults = await healthChecker.checkAllProxies(proxies);

    res.json({
      success: true,
      message: '健康检查完成',
      data: {
        health: healthResults,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('手动健康检查失败', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
}));

/**
 * 获取系统状态
 * GET /api/status
 */
router.get('/status', asyncHandler(async (req, res) => {
  const proxies = await configManager.getProxies();
  const activeProxy = await configManager.getActiveProxy();
  const settings = await configManager.getSettings();
  const healthStatus = healthChecker.getAllHealthStatus();

  res.json({
    success: true,
    data: {
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: require('../package.json').version,
        nodeVersion: process.version,
        platform: process.platform
      },
      proxy: {
        total: proxies.length,
        active: activeProxy ? activeProxy.id : null,
        activeProxyName: activeProxy ? activeProxy.name : null
      },
      health: {
        total: healthStatus.length,
        healthy: healthStatus.filter(h => h.status === 'healthy').length,
        unhealthy: healthStatus.filter(h => h.status === 'unhealthy').length
      },
      settings: settings
    }
  });
}));

/**
 * 更新代理配置
 * PUT /api/proxies/:id
 */
router.put('/proxies/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const updatedProxy = await configManager.updateProxy(id, req.body);

    // 如果更新的是当前激活的代理，重置代理处理器以应用新配置
    const currentProxy = proxyHandler.getCurrentProxy();
    if (currentProxy && currentProxy.id === id) {
      proxyHandler.resetProxy();
    }

    logger.info(`代理更新成功: ${updatedProxy.name} (${id})`);

    res.json({
      success: true,
      message: `代理 "${updatedProxy.name}" 更新成功`,
      data: {
        proxy: updatedProxy
      }
    });
  } catch (error) {
    logger.error(`代理更新失败: ${id}`, error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
}));

module.exports = router;
