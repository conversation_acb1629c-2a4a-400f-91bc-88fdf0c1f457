const express = require('express');
const router = express.Router();

// 导入工具和中间件
const proxyHandler = require('../utils/proxyHandler');
const { proxyLogger } = require('../middleware/logger');
const { asyncHandler } = require('../middleware/errorHandler');

/**
 * 代理状态检查中间件
 */
const checkProxyStatus = asyncHandler(async (req, res, next) => {
  const hasActiveProxy = await proxyHandler.hasActiveProxy();
  
  if (!hasActiveProxy) {
    return res.status(503).json({
      error: '代理服务不可用',
      message: '当前没有激活的代理接口，请在管理界面启用一个代理',
      code: 'NO_ACTIVE_PROXY'
    });
  }
  
  next();
});

/**
 * 代理信息中间件
 */
const addProxyInfo = (req, res, next) => {
  const currentProxy = proxyHandler.getCurrentProxy();
  if (currentProxy) {
    req.proxyInfo = {
      id: currentProxy.id,
      name: currentProxy.name,
      url: currentProxy.url
    };
  }
  next();
};

/**
 * 所有代理请求的通用处理
 * 路径: /proxy/*
 */
router.use('*',
  proxyLogger,           // 代理请求日志
  checkProxyStatus,      // 检查代理状态
  addProxyInfo,          // 添加代理信息
  asyncHandler(async (req, res, next) => {
    // 使用动态代理处理请求
    await proxyHandler.dynamicProxy(req, res, next);
  })
);

module.exports = router;
