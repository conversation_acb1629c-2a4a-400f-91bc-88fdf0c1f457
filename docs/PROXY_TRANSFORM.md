# HaoProxy 代理转换功能

HaoProxy 提供了强大的API请求转换功能，可以根据配置文件自动转换请求的URL、API Key和模型名称，让你能够无缝地在不同的AI服务提供商之间切换。

## 功能特性

### 🔄 三重转换机制

1. **URL替换**：将原始请求的URL替换为配置中指定的目标URL
2. **API Key替换**：将请求头中的Authorization Bearer token替换为配置中指定的apiKey
3. **模型名称替换**：将请求体JSON中的"model"字段值替换为配置中指定的model名称
4. **其他信息保持不变**：除了上述三项外，请求的其他所有信息都保持原样

## 配置说明

### 配置文件结构

配置文件位于 `config/proxies.json`：

```json
{
  "proxies": [
    {
      "id": "openai-api",
      "name": "Qwen3-Coder-480B-A35B-Instruct",
      "url": "https://api-inference.modelscope.cn/v1/",
      "enabled": true,
      "ignorePath": true,
      "timeout": 30000,
      "description": "Qwen3-Coder-480B-A35B-Instruct",
      "apiKey": "ms-58bf6d9d-a9ab-47af-8433-91fbf1bbd2cc",
      "model": "Qwen/Qwen3-Coder-480B-A35B-Instruct"
    }
  ]
}
```

### 配置字段说明

- `url`: 目标API服务器的基础URL
- `apiKey`: 目标API服务器的认证密钥
- `model`: 目标API服务器支持的模型名称
- `ignorePath`: 是否忽略原始请求路径（true时直接使用配置的URL）
- `enabled`: 是否启用此代理配置

## 使用方法

### 1. 启动服务器

```bash
npm run dev
```

### 2. 发送API请求

将原本发送到AI服务商的请求改为发送到HaoProxy：

**原始请求：**
```bash
curl https://api.openai.com/v1/chat/completions \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer your-original-api-key" \
    -d '{
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "user", "content": "Hello!"}
        ]
    }'
```

**通过HaoProxy的请求：**
```bash
curl http://localhost:3000/proxy/v1/chat/completions \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer any-key-will-be-replaced" \
    -d '{
        "model": "any-model-will-be-replaced",
        "messages": [
            {"role": "user", "content": "Hello!"}
        ]
    }'
```

### 3. 转换过程

HaoProxy会自动进行以下转换：

1. **URL转换**: `http://localhost:3000/proxy/v1/chat/completions` → `https://api-inference.modelscope.cn/v1/`
2. **API Key转换**: `Bearer any-key-will-be-replaced` → `Bearer ms-58bf6d9d-a9ab-47af-8433-91fbf1bbd2cc`
3. **模型转换**: `"model": "any-model-will-be-replaced"` → `"model": "Qwen/Qwen3-Coder-480B-A35B-Instruct"`

## 测试和演示

### 运行测试

```bash
node test/proxy-transform-test.js
```

### 运行演示

```bash
node demo/proxy-demo.js
```

## 日志监控

HaoProxy会记录详细的转换日志，你可以通过以下方式查看：

1. **实时日志**：查看服务器控制台输出
2. **日志文件**：查看 `logs/app-YYYY-MM-DD.log`

### 日志示例

```
{"level":"info","message":"模型替换: gpt-3.5-turbo -> Qwen/Qwen3-Coder-480B-A35B-Instruct","timestamp":"2025-08-02 21:46:14"}
{"level":"info","message":"代理请求转换 [Qwen3-Coder-480B-A35B-Instruct]: POST /proxy/v1/chat/completions -> https://api-inference.modelscope.cn/v1/","timestamp":"2025-08-02 21:46:14"}
{"level":"info","message":"代理响应 [Qwen3-Coder-480B-A35B-Instruct]: 200 POST /proxy/v1/chat/completions","timestamp":"2025-08-02 21:46:14"}
```

## 错误处理

HaoProxy提供了完善的错误处理机制：

- **连接失败**: 返回502状态码，提示目标服务器连接失败
- **请求超时**: 返回504状态码，提示请求超时
- **其他错误**: 返回500状态码，包含详细错误信息

## Web管理界面

访问 `http://localhost:3000` 可以使用Web界面管理代理配置：

- 查看所有代理配置
- 启用/禁用代理
- 添加新的代理配置
- 编辑现有配置

## 高级用法

### 多代理切换

你可以配置多个代理，并通过Web界面或API动态切换：

```bash
# 切换到指定代理
curl -X PUT http://localhost:3000/api/proxies/openai-api/toggle
```

### 路径处理

- `ignorePath: true`: 忽略原始路径，直接使用配置的URL
- `ignorePath: false`: 保留原始路径，拼接到配置的URL后面

## 注意事项

1. **安全性**: 请妥善保管配置文件中的API密钥
2. **兼容性**: 确保目标API与原始API的接口格式兼容
3. **监控**: 建议监控日志以确保转换正常工作
4. **测试**: 在生产环境使用前请充分测试

## 故障排除

### 常见问题

1. **404错误**: 检查目标API的URL和路径是否正确
2. **401错误**: 检查API密钥是否有效
3. **超时错误**: 检查网络连接和超时设置
4. **模型错误**: 确认目标API支持配置的模型名称

### 调试技巧

1. 查看服务器日志了解详细的转换过程
2. 使用测试脚本验证功能
3. 检查配置文件格式是否正确
4. 确认代理配置已启用
