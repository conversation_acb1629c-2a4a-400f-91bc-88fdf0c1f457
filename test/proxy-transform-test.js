const axios = require('axios');

/**
 * 代理转换功能测试脚本
 * 测试URL替换、API Key替换、模型名称替换功能
 */

const PROXY_BASE_URL = 'http://localhost:3000/proxy';

// 测试数据
const testRequest = {
  model: 'gpt-3.5-turbo',
  messages: [
    {
      role: 'system',
      content: '你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。'
    },
    {
      role: 'user',
      content: '你好，我叫李雷，1+1等于多少？'
    }
  ],
  temperature: 0.6
};

/**
 * 测试代理转换功能
 */
async function testProxyTransform() {
  console.log('🚀 开始测试代理转换功能...\n');

  try {
    // 测试1: 基本的API请求转换
    console.log('📝 测试1: 基本API请求转换');
    console.log('原始请求数据:', JSON.stringify(testRequest, null, 2));
    console.log('原始Authorization: Bearer original-api-key-should-be-replaced');

    const response = await axios.post(`${PROXY_BASE_URL}/v1/chat/completions`, testRequest, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer original-api-key-should-be-replaced'
      },
      timeout: 30000,
      validateStatus: () => true // 接受所有状态码
    });

    console.log('响应状态:', response.status);
    console.log('响应头:', {
      'X-Proxy-By': response.headers['x-proxy-by'],
      'X-Proxy-Target': response.headers['x-proxy-target'],
      'Content-Type': response.headers['content-type']
    });

    // 检查代理转换是否成功
    const proxyWorking = response.headers['x-proxy-by'] === 'HaoProxy' &&
                        response.headers['x-proxy-target'] === 'Qwen3-Coder-480B-A35B-Instruct';

    if (proxyWorking) {
      console.log('✅ 测试1通过: 代理转换功能正常工作');
      console.log('  - URL替换: ✅ 请求已转发到配置的目标URL');
      console.log('  - API Key替换: ✅ Authorization头已被替换');
      console.log('  - 模型替换: ✅ 模型名称已被替换（查看服务器日志确认）');

      if (response.status === 200) {
        console.log('  - 目标API响应: ✅ 成功');
        console.log('响应数据示例:', JSON.stringify(response.data, null, 2).substring(0, 500) + '...');
      } else {
        console.log(`  - 目标API响应: ⚠️  状态码 ${response.status} (可能是目标API的正常响应)`);
      }
    } else {
      console.log('❌ 测试1失败: 代理转换未正常工作');
    }

  } catch (error) {
    console.error('❌ 测试1异常:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  try {
    // 测试2: 测试不同的模型名称
    console.log('📝 测试2: 不同模型名称转换');
    const testRequest2 = {
      ...testRequest,
      model: 'gpt-4'
    };
    
    console.log('原始模型名称:', testRequest2.model);
    
    const response2 = await axios.post(`${PROXY_BASE_URL}/v1/chat/completions`, testRequest2, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-key-2'
      },
      timeout: 30000,
      validateStatus: () => true
    });

    console.log('响应状态:', response2.status);
    console.log('✅ 测试2完成: 模型名称转换测试');

  } catch (error) {
    console.error('❌ 测试2异常:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  try {
    // 测试3: 测试GET请求（健康检查）
    console.log('📝 测试3: GET请求代理');
    
    const response3 = await axios.get(`${PROXY_BASE_URL}/health`, {
      headers: {
        'Authorization': 'Bearer test-get-key'
      },
      timeout: 30000,
      validateStatus: () => true
    });

    console.log('响应状态:', response3.status);
    console.log('✅ 测试3完成: GET请求代理测试');

  } catch (error) {
    console.error('❌ 测试3异常:', error.message);
  }

  console.log('\n🎉 代理转换功能测试完成！');
}

/**
 * 检查服务器状态
 */
async function checkServerStatus() {
  try {
    const response = await axios.get('http://localhost:3000/health', {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log('✅ 服务器运行正常');
      return true;
    }
  } catch (error) {
    console.error('❌ 服务器未运行或无法访问');
    console.error('请确保服务器已启动: npm run dev');
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🔍 检查服务器状态...');
  
  const serverRunning = await checkServerStatus();
  if (!serverRunning) {
    process.exit(1);
  }

  console.log('\n' + '='.repeat(50) + '\n');
  
  await testProxyTransform();
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testProxyTransform,
  checkServerStatus
};
