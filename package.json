{"name": "haoproxy", "version": "1.0.0", "description": "AI接口代理管理系统 - 支持多个AI接口的动态代理转发和Web管理界面", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["proxy", "ai", "api", "management", "express"], "author": "HaoProxy Team", "license": "MIT", "dependencies": {"axios": "^1.11.0", "cors": "^2.8.5", "express": "^4.18.2", "helmet": "^8.1.0", "http-proxy-middleware": "^3.0.5", "morgan": "^1.10.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}