const { logger } = require('./logger');

/**
 * 404错误处理中间件
 */
const notFoundHandler = (req, res, next) => {
  const error = new Error(`路径 ${req.originalUrl} 未找到`);
  error.status = 404;
  next(error);
};

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  // 设置默认错误状态码
  const status = err.status || err.statusCode || 500;
  
  // 记录错误日志
  logger.error('应用错误', {
    error: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // 构建错误响应
  const errorResponse = {
    error: true,
    message: err.message || '内部服务器错误',
    status: status,
    timestamp: new Date().toISOString()
  };

  // 在开发环境中包含错误堆栈
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
  }

  // 发送错误响应
  res.status(status).json(errorResponse);
};

/**
 * 异步错误捕获包装器
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 验证请求体中间件
 */
const validateRequestBody = (requiredFields = []) => {
  return (req, res, next) => {
    const missingFields = requiredFields.filter(field => !req.body[field]);
    
    if (missingFields.length > 0) {
      const error = new Error(`缺少必要字段: ${missingFields.join(', ')}`);
      error.status = 400;
      return next(error);
    }
    
    next();
  };
};

/**
 * 请求大小限制错误处理
 */
const handlePayloadTooLarge = (err, req, res, next) => {
  if (err.type === 'entity.too.large') {
    return res.status(413).json({
      error: true,
      message: '请求体过大',
      maxSize: '100MB'
    });
  }
  next(err);
};

module.exports = {
  notFoundHandler,
  errorHandler,
  asyncHandler,
  validateRequestBody,
  handlePayloadTooLarge
};
