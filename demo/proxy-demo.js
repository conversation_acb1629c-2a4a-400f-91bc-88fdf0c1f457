const axios = require('axios');

/**
 * HaoProxy 代理转换功能演示
 * 
 * 此演示展示了如何使用HaoProxy进行API请求转换：
 * 1. URL替换：将请求转发到配置的目标URL
 * 2. API Key替换：替换Authorization头中的Bearer token
 * 3. 模型名称替换：替换请求体中的model字段
 */

const PROXY_URL = 'http://localhost:3000/proxy';

async function demonstrateProxyTransform() {
  console.log('🎯 HaoProxy 代理转换功能演示\n');
  console.log('=' * 50);

  // 原始请求数据
  const originalRequest = {
    model: 'gpt-3.5-turbo',  // 这将被替换为配置中的模型
    messages: [
      {
        role: 'system',
        content: '你是一个有用的AI助手。'
      },
      {
        role: 'user',
        content: '你好，请介绍一下你自己。'
      }
    ],
    temperature: 0.7,
    max_tokens: 150
  };

  console.log('📋 原始请求信息:');
  console.log('  URL: /v1/chat/completions');
  console.log('  Authorization: Bearer original-test-key-12345');
  console.log('  Model:', originalRequest.model);
  console.log('  Messages:', originalRequest.messages.length, '条消息');
  console.log();

  try {
    console.log('🚀 发送代理请求...');
    
    const response = await axios.post(`${PROXY_URL}/v1/chat/completions`, originalRequest, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer original-test-key-12345'  // 这将被替换
      },
      timeout: 30000,
      validateStatus: () => true
    });

    console.log('\n📥 代理转换结果:');
    console.log('  响应状态:', response.status);
    console.log('  代理服务:', response.headers['x-proxy-by']);
    console.log('  目标服务:', response.headers['x-proxy-target']);
    
    if (response.status === 200) {
      console.log('  ✅ 请求成功处理');
      
      // 显示响应数据的一部分
      if (response.data && response.data.choices) {
        console.log('  📝 AI响应预览:');
        console.log('    ', response.data.choices[0]?.message?.content?.substring(0, 100) + '...');
      }
    } else {
      console.log('  ⚠️  目标API返回状态码:', response.status);
      console.log('  这可能是正常的，取决于目标API的配置');
    }

    console.log('\n🔄 转换过程说明:');
    console.log('  1. URL转换: /proxy/v1/chat/completions → 配置的目标URL');
    console.log('  2. API Key转换: original-test-key-12345 → 配置的apiKey');
    console.log('  3. 模型转换: gpt-3.5-turbo → 配置的model名称');
    console.log('  4. 其他数据: 保持原样传递');

  } catch (error) {
    console.error('\n❌ 请求失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('  请确保HaoProxy服务器正在运行: npm run dev');
    } else if (error.response) {
      console.error('  响应状态:', error.response.status);
      console.error('  错误详情:', error.response.data);
    }
  }

  console.log('\n' + '=' * 50);
  console.log('💡 提示:');
  console.log('  - 查看服务器日志可以看到详细的转换过程');
  console.log('  - 在config/proxies.json中配置不同的代理目标');
  console.log('  - 使用Web界面 http://localhost:3000 管理代理配置');
}

// 检查服务器状态
async function checkServer() {
  try {
    await axios.get('http://localhost:3000/health', { timeout: 5000 });
    return true;
  } catch (error) {
    console.error('❌ 无法连接到HaoProxy服务器');
    console.error('   请先启动服务器: npm run dev');
    return false;
  }
}

// 主函数
async function main() {
  console.log('🔍 检查服务器状态...');
  
  if (await checkServer()) {
    console.log('✅ 服务器运行正常\n');
    await demonstrateProxyTransform();
  } else {
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { demonstrateProxyTransform };
