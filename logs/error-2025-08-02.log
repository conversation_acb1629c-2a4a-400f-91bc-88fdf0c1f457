{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:30:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:30:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:31:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:31:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /favicon.ico 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /favicon.ico 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 20:32:10","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:32:10","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:32:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:32:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:33:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:33:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:34:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:34:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2478ms","ip":"::1","level":"error","message":"请求错误","method":"POST","statusCode":405,"timestamp":"2025-08-02 20:34:45","url":"/","userAgent":"curl/8.7.1"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:35:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:35:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /favicon.ico 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /favicon.ico 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 20:53:02","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:53:02","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 //models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 //models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 20:59:48","url":"//models","userAgent":"axios/1.10.0"}
{"duration":"3ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:59:48","url":"//models","userAgent":"axios/1.10.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:00:09","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:00:09","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"error":"路径 //models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 //models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:00:18","url":"//models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:00:18","url":"//models","userAgent":"axios/1.10.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:00:23","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:00:23","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:00:40","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:00:40","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:00:50","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:00:50","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:01:39","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:01:39","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:01:41","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"0ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:01:41","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:02:00","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:02:00","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:02:02","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:02:02","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:02:05","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:02:05","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"error":"路径 /.well-known/appspecific/com.chrome.devtools.json 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /.well-known/appspecific/com.chrome.devtools.json 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:03:01","url":"/.well-known/appspecific/com.chrome.devtools.json","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:03:01","url":"/.well-known/appspecific/com.chrome.devtools.json","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:04:44","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:04:44","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"188ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:23:57","url":"","userAgent":"curl/8.7.1"}
{"duration":"96ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:24:07","url":"","userAgent":"curl/8.7.1"}
{"error":"路径 //models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 //models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:26:21","url":"//models","userAgent":"axios/1.10.0"}
{"duration":"3ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:26:21","url":"//models","userAgent":"axios/1.10.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:26:52","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:26:52","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"164ms","ip":"::1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:46:14","url":"/","userAgent":"axios/1.11.0"}
{"duration":"27ms","ip":"::1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:46:14","url":"/","userAgent":"axios/1.11.0"}
{"duration":"26ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:46:14","url":"/","userAgent":"axios/1.11.0"}
{"duration":"81ms","ip":"::1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:47:59","url":"/","userAgent":"axios/1.11.0"}
{"error":"路径 //models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 //models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:51:15","url":"//models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:51:15","url":"//models","userAgent":"axios/1.10.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:51:23","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:51:23","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"error":"路径 /.well-known/appspecific/com.chrome.devtools.json 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /.well-known/appspecific/com.chrome.devtools.json 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:51:36","url":"/.well-known/appspecific/com.chrome.devtools.json","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:51:36","url":"/.well-known/appspecific/com.chrome.devtools.json","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:53:37","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"3ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:53:37","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
