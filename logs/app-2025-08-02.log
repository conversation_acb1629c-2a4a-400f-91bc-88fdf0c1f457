{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 20:30:11"}
{"level":"info","message":"HaoProxy服务器启动成功","timestamp":"2025-08-02 20:30:11"}
{"level":"info","message":"服务器地址: http://localhost:3000","timestamp":"2025-08-02 20:30:11"}
{"level":"info","message":"管理界面: http://localhost:3000","timestamp":"2025-08-02 20:30:11"}
{"level":"info","message":"API文档: http://localhost:3000/api","timestamp":"2025-08-02 20:30:11"}
{"level":"info","message":"代理端点: http://localhost:3000/proxy/*","timestamp":"2025-08-02 20:30:11"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:30:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:30:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:30:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 20:30:50"}
{"level":"info","message":"HaoProxy服务器启动成功","timestamp":"2025-08-02 20:30:50"}
{"level":"info","message":"服务器地址: http://localhost:3000","timestamp":"2025-08-02 20:30:50"}
{"level":"info","message":"管理界面: http://localhost:3000","timestamp":"2025-08-02 20:30:50"}
{"level":"info","message":"API文档: http://localhost:3000/api","timestamp":"2025-08-02 20:30:50"}
{"level":"info","message":"代理端点: http://localhost:3000/proxy/*","timestamp":"2025-08-02 20:30:50"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:31:09","url":"/api","userAgent":"curl/8.7.1"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:31:09","url":"/","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:31:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:31:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:31:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:31:19","url":"/api/proxies","userAgent":"curl/8.7.1"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:31:19","url":"/proxies","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:31:24","url":"/api/health","userAgent":"curl/8.7.1"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:31:24","url":"/health","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:31:34","url":"/api/proxies/openai-api/toggle","userAgent":"curl/8.7.1"}
{"level":"info","message":"代理切换成功: OpenAI API (openai-api)","timestamp":"2025-08-02 20:31:34"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:31:34","url":"/proxies/openai-api/toggle","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:31:39","url":"/proxy/v1/models","userAgent":"curl/8.7.1"}
{"headers":{"accept":"*/*","host":"localhost:3000","user-agent":"curl/8.7.1"},"ip":"::1","level":"info","message":"代理请求","method":"GET","originalUrl":"/proxy/v1/models","path":"/","timestamp":"2025-08-02 20:31:39"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:08","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:32:08","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:08","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:08","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:32:08","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:32:08","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:10","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:32:10","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:32:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:10","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:32:10","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:32:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:10","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /favicon.ico 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /favicon.ico 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 20:32:10","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:32:10","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 20:32:16","url":"/api/health/check","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:32:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:32:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"healthy":0,"level":"info","message":"健康检查完成","timestamp":"2025-08-02 20:32:26","total":3,"unhealthy":3}
{"duration":"10029ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":200,"timestamp":"2025-08-02 20:32:26","url":"/health/check","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:40","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:32:40","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:32:40","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:32:40","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:33:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:33:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 20:33:16","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"新代理添加成功: 测试API (test-api)","timestamp":"2025-08-02 20:33:16"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":201,"timestamp":"2025-08-02 20:33:16","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:16","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:33:16","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:33:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:33:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:33:28","url":"/api/proxies/test-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理切换成功: 测试API (test-api)","timestamp":"2025-08-02 20:33:28"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:33:28","url":"/proxies/test-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:28","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:33:28","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:38","url":"/proxy/get","userAgent":"curl/8.7.1"}
{"headers":{"accept":"*/*","host":"localhost:3000","user-agent":"curl/8.7.1"},"ip":"::1","level":"info","message":"代理请求","method":"GET","originalUrl":"/proxy/get","path":"/","timestamp":"2025-08-02 20:33:38"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:40","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:33:40","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:40","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:33:40","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3011ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:33:41","url":"/","userAgent":"curl/8.7.1"}
{"duration":"3011ms","level":"info","message":"代理响应","method":"GET","originalUrl":"/proxy/get","statusCode":200,"timestamp":"2025-08-02 20:33:41"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:50","url":"/proxy/json","userAgent":"curl/8.7.1"}
{"headers":{"accept":"*/*","host":"localhost:3000","user-agent":"curl/8.7.1"},"ip":"::1","level":"info","message":"代理请求","method":"GET","originalUrl":"/proxy/json","path":"/","timestamp":"2025-08-02 20:33:50"}
{"duration":"1846ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:33:51","url":"/","userAgent":"curl/8.7.1"}
{"duration":"1847ms","level":"info","message":"代理响应","method":"GET","originalUrl":"/proxy/json","statusCode":200,"timestamp":"2025-08-02 20:33:51"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:33:58","url":"/proxy/ip","userAgent":"curl/8.7.1"}
{"headers":{"accept":"application/json","host":"localhost:3000","user-agent":"curl/8.7.1"},"ip":"::1","level":"info","message":"代理请求","method":"GET","originalUrl":"/proxy/ip","path":"/","timestamp":"2025-08-02 20:33:58"}
{"duration":"2386ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:34:01","url":"/","userAgent":"curl/8.7.1"}
{"duration":"2386ms","level":"info","message":"代理响应","method":"GET","originalUrl":"/proxy/ip","statusCode":200,"timestamp":"2025-08-02 20:34:01"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:34:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:34:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:34:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:34:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:34:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:34:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:34:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:34:40","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:34:40","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:34:40","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:34:40","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 20:34:42","url":"/proxy/post","userAgent":"curl/8.7.1"}
{"headers":{"accept":"*/*","content-length":"16","content-type":"application/json","host":"localhost:3000","user-agent":"curl/8.7.1"},"ip":"::1","level":"info","message":"代理请求","method":"POST","originalUrl":"/proxy/post","path":"/","timestamp":"2025-08-02 20:34:42"}
{"duration":"2478ms","ip":"::1","level":"error","message":"请求错误","method":"POST","statusCode":405,"timestamp":"2025-08-02 20:34:45","url":"/","userAgent":"curl/8.7.1"}
{"duration":"2478ms","level":"info","message":"代理响应","method":"POST","originalUrl":"/proxy/post","statusCode":405,"timestamp":"2025-08-02 20:34:45"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:35:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:35:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:35:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:35:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:35:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /api/proxies/health 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /api/proxies/health 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:47:12)","timestamp":"2025-08-02 20:35:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:35:17","url":"/api/proxies/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"DELETE","timestamp":"2025-08-02 20:35:31","url":"/api/proxies/test-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理删除成功: 测试API (test-api)","timestamp":"2025-08-02 20:35:31"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"DELETE","statusCode":200,"timestamp":"2025-08-02 20:35:31","url":"/proxies/test-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:35:31","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:35:31","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:35:40","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:35:40","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:35:40","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:35:40","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:35:43","url":"/api/status","userAgent":"curl/8.7.1"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:35:43","url":"/status","userAgent":"curl/8.7.1"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 20:37:16"}
{"level":"info","message":"HaoProxy服务器启动成功","timestamp":"2025-08-02 20:37:16"}
{"level":"info","message":"服务器地址: http://localhost:3000","timestamp":"2025-08-02 20:37:16"}
{"level":"info","message":"管理界面: http://localhost:3000","timestamp":"2025-08-02 20:37:16"}
{"level":"info","message":"API文档: http://localhost:3000/api","timestamp":"2025-08-02 20:37:16"}
{"level":"info","message":"代理端点: http://localhost:3000/proxy/*","timestamp":"2025-08-02 20:37:16"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:37:20","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:37:20","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:37:20","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:37:20","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:37:20","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:37:20","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:37:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:37:21","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:37:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:37:21","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:37:21","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:37:21","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:37:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:37:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:37:51","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:37:51","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:37:51","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:37:51","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:38:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:38:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:38:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:38:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:38:51","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:38:51","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:38:51","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:38:51","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:39:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:39:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:39:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:39:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 20:39:33","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"新代理添加成功: Qwen3-Coder-480B-A35B-Instruct (111)","timestamp":"2025-08-02 20:39:33"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":201,"timestamp":"2025-08-02 20:39:33","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:39:33","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:39:33","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 20:39:40","url":"/api/proxies/111/test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"229ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":200,"timestamp":"2025-08-02 20:39:40","url":"/proxies/111/test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:39:40","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:39:40","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:39:51","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:39:51","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:39:51","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:39:51","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 20:40:00","url":"/api/health/check","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"healthy":0,"level":"info","message":"健康检查完成","timestamp":"2025-08-02 20:40:10","total":4,"unhealthy":4}
{"duration":"10101ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":200,"timestamp":"2025-08-02 20:40:10","url":"/health/check","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:40:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:40:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:40:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:40:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:40:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:40:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:40:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:40:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:41:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:41:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:41:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:41:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:41:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:41:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:41:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:41:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:42:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:42:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:42:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:42:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:42:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:42:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:42:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:42:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:43:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:43:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:43:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:43:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:43:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:43:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:43:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:43:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:44:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:44:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:44:21","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:44:21","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:44:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:44:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:44:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:44:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:45:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:45:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:45:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:45:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:46:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:46:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:46:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:46:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:47:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:47:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:47:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:47:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:48:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:48:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:48:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:48:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:49:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:49:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:49:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:49:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:49:45","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:49:45","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:49:45","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:49:45","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:49:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:49:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:49:52","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:49:52","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:50:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:50:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:50:22","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:50:22","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:51:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:51:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:51:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:51:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 20:51:23"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 20:51:45"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 20:52:01"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 20:52:15"}
{"level":"info","message":"HaoProxy服务器启动成功","timestamp":"2025-08-02 20:52:15"}
{"level":"info","message":"服务器地址: http://localhost:3000","timestamp":"2025-08-02 20:52:15"}
{"level":"info","message":"管理界面: http://localhost:3000","timestamp":"2025-08-02 20:52:15"}
{"level":"info","message":"API文档: http://localhost:3000/api","timestamp":"2025-08-02 20:52:15"}
{"level":"info","message":"代理端点: http://localhost:3000/proxy/*","timestamp":"2025-08-02 20:52:15"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:52:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:52:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:52:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:52:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:52:36","url":"/api/proxies","userAgent":"curl/8.7.1"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:52:36","url":"/proxies","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 20:52:43","url":"/api/proxies","userAgent":"curl/8.7.1"}
{"level":"info","message":"新代理添加成功: 测试API增强版 (test-api-enhanced)","timestamp":"2025-08-02 20:52:43"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":201,"timestamp":"2025-08-02 20:52:43","url":"/proxies","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:52:49","url":"/api/proxies/test-api-enhanced","userAgent":"curl/8.7.1"}
{"level":"info","message":"代理更新成功: 测试API增强版（已更新） (test-api-enhanced)","timestamp":"2025-08-02 20:52:49"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:52:49","url":"/proxies/test-api-enhanced","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:52:56","url":"/api/proxies","userAgent":"curl/8.7.1"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:52:56","url":"/proxies","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:01","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:53:01","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:01","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:01","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:53:01","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:53:01","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:02","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:02","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:53:02","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:53:02","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:02","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:53:02","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:02","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:53:02","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:02","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /favicon.ico 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /favicon.ico 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 20:53:02","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:53:02","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:53:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:53:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:32","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:53:32","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:53:32","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:53:32","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:54:02","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:54:02","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:54:02","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:54:02","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:54:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:54:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:54:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:54:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:54:24","url":"/api/proxies/openai-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理更新成功: OpenAI API (openai-api)","timestamp":"2025-08-02 20:54:24"}
{"duration":"5ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:54:24","url":"/proxies/openai-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:54:24","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:54:24","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:54:32","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:54:32","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:54:32","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:54:32","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:55:02","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:55:02","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:55:02","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:55:02","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:55:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:55:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:55:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:55:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 20:55:27","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"新代理添加成功: Cohere API (cohere-api)","timestamp":"2025-08-02 20:55:27"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":201,"timestamp":"2025-08-02 20:55:27","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:55:27","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:55:27","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:55:32","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:55:32","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:55:32","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:55:32","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:55:40","url":"/api/proxies","userAgent":"curl/8.7.1"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:55:40","url":"/proxies","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:56:02","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:56:02","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:56:02","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:56:02","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 20:57:15"}
{"level":"info","message":"HaoProxy服务器启动成功","timestamp":"2025-08-02 20:57:15"}
{"level":"info","message":"服务器地址: http://localhost:3000","timestamp":"2025-08-02 20:57:15"}
{"level":"info","message":"管理界面: http://localhost:3000","timestamp":"2025-08-02 20:57:15"}
{"level":"info","message":"API文档: http://localhost:3000/api","timestamp":"2025-08-02 20:57:15"}
{"level":"info","message":"代理端点: http://localhost:3000/proxy/*","timestamp":"2025-08-02 20:57:15"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:57:19","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:57:19","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:57:19","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:57:19","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:57:19","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:57:19","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:57:20","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:57:20","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:57:20","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:57:20","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:57:20","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:57:20","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:57:20","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:57:20","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:57:50","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:57:50","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:57:50","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:57:50","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:58:20","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:58:20","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:58:20","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:58:20","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:58:47","url":"/api/proxies/openai-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理更新成功: Qwen3-Coder-480B-A35B-Instruct (openai-api)","timestamp":"2025-08-02 20:58:47"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:58:47","url":"/proxies/openai-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:58:47","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:58:47","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:58:50","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:58:50","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:58:50","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:58:50","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:58:51","url":"/api/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理切换成功: Qwen3-Coder-480B-A35B-Instruct (openai-api)","timestamp":"2025-08-02 20:58:51"}
{"duration":"5ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:58:51","url":"/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:58:51","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:58:51","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 20:58:54","url":"/api/proxies/openai-api/test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"226ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":200,"timestamp":"2025-08-02 20:58:54","url":"/proxies/openai-api/test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:58:54","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:58:54","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:05","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:05","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:05","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:05","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:05","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:05","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:07","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:07","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:07","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:59:07","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:07","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:07","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:07","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:07","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:59:17","url":"/api/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理切换成功: Qwen3-Coder-480B-A35B-Instruct (openai-api)","timestamp":"2025-08-02 20:59:17"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:59:17","url":"/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:17","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:17","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:59:21","url":"/api/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理切换成功: Qwen3-Coder-480B-A35B-Instruct (openai-api)","timestamp":"2025-08-02 20:59:21"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:59:21","url":"/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:21","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:21","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:59:22","url":"/api/proxies/claude-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理切换成功: Claude API (claude-api)","timestamp":"2025-08-02 20:59:22"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:59:22","url":"/proxies/claude-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:22","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:59:22","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:59:26","url":"/api/proxies/gemini-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理切换成功: Gemini API (gemini-api)","timestamp":"2025-08-02 20:59:26"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:59:26","url":"/proxies/gemini-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:26","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:59:26","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 20:59:28","url":"/api/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理切换成功: Qwen3-Coder-480B-A35B-Instruct (openai-api)","timestamp":"2025-08-02 20:59:28"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 20:59:28","url":"/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:28","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 20:59:28","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:37","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:37","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:37","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 20:59:37","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 20:59:48","url":"//models","userAgent":"axios/1.10.0"}
{"error":"路径 //models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 //models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 20:59:48","url":"//models","userAgent":"axios/1.10.0"}
{"duration":"3ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 20:59:48","url":"//models","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:00:08","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:00:08","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:00:08","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:00:08","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 21:00:09","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:00:09","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:00:09","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:00:18","url":"//models","userAgent":"axios/1.10.0"}
{"error":"路径 //models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 //models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:00:18","url":"//models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:00:18","url":"//models","userAgent":"axios/1.10.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:00:23","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:00:23","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:00:23","url":"/models","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:00:37","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:00:37","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:00:37","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:00:37","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 21:00:40","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:00:40","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:00:40","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:00:50","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:00:50","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:00:50","url":"/models","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:01:08","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:01:08","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:01:08","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:01:08","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:01:37","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:01:37","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:01:37","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:01:37","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:01:39","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:01:39","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:01:39","url":"/models","userAgent":"axios/1.10.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 21:01:41","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:01:41","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"0ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:01:41","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:02:00","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:02:00","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:02:00","url":"/models","userAgent":"axios/1.10.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:02:02","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:02:02","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:02:02","url":"/models","userAgent":"axios/1.10.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 21:02:05","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"error":"路径 /chat/completions 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"POST","stack":"Error: 路径 /chat/completions 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-08-02 21:02:05","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"duration":"1ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"POST","statusCode":404,"timestamp":"2025-08-02 21:02:05","url":"/chat/completions","userAgent":"Hs/JS 4.83.0"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:02:08","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:02:08","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:02:08","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:02:08","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 21:02:19","url":"/api/proxies/openai-api/test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"90ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":200,"timestamp":"2025-08-02 21:02:19","url":"/proxies/openai-api/test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:02:19","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:02:19","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"DELETE","timestamp":"2025-08-02 21:02:30","url":"/api/proxies/cohere-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理删除成功: Cohere API (cohere-api)","timestamp":"2025-08-02 21:02:30"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"DELETE","statusCode":200,"timestamp":"2025-08-02 21:02:30","url":"/proxies/cohere-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:02:30","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:02:30","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"DELETE","timestamp":"2025-08-02 21:02:37","url":"/api/proxies/test-api-enhanced","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理删除成功: 测试API增强版（已更新） (test-api-enhanced)","timestamp":"2025-08-02 21:02:37"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"DELETE","statusCode":200,"timestamp":"2025-08-02 21:02:37","url":"/proxies/test-api-enhanced","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:02:37","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:02:37","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"DELETE","timestamp":"2025-08-02 21:02:48","url":"/api/proxies/claude-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理删除成功: Claude API (claude-api)","timestamp":"2025-08-02 21:02:48"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"DELETE","statusCode":200,"timestamp":"2025-08-02 21:02:48","url":"/proxies/claude-api","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:02:48","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:02:48","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:03:01","url":"/.well-known/appspecific/com.chrome.devtools.json","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"路径 /.well-known/appspecific/com.chrome.devtools.json 未找到","ip":"::1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /.well-known/appspecific/com.chrome.devtools.json 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:03:01","url":"/.well-known/appspecific/com.chrome.devtools.json","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:03:01","url":"/.well-known/appspecific/com.chrome.devtools.json","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:03:01","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:03:01","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:03:07","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:03:07","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:03:07","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:03:07","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 21:03:13","url":"/api/proxies/openai-api/test","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"110ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":200,"timestamp":"2025-08-02 21:03:13","url":"/proxies/openai-api/test","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:03:13","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:03:13","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:03:37","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:03:37","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:03:37","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:03:37","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:04:07","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:04:07","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:04:07","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:04:07","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:04:38","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:04:38","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:04:38","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:04:38","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:04:44","url":"/models","userAgent":"axios/1.10.0"}
{"error":"路径 /models 未找到","ip":"::ffff:127.0.0.1","level":"error","message":"应用错误","method":"GET","stack":"Error: 路径 /models 未找到\n    at notFoundHandler (/Users/<USER>/Projects/Web/HaoProxy/middleware/errorHandler.js:7:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Projects/Web/HaoProxy/node_modules/express/lib/router/index.js:280:10)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/serve-static/index.js:121:7)\n    at SendStream.emit (node:events:518:28)\n    at SendStream.error (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:270:17)\n    at SendStream.onStatError (/Users/<USER>/Projects/Web/HaoProxy/node_modules/send/index.js:417:12)","timestamp":"2025-08-02 21:04:44","url":"/models","userAgent":"axios/1.10.0"}
{"duration":"2ms","ip":"::ffff:127.0.0.1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:04:44","url":"/models","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:05:07","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:05:07","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:05:07","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:05:07","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:05:38","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:05:38","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:05:38","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:05:38","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:06:08","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:06:08","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:06:08","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:06:08","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:07:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:07:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:07:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:07:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:08:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:08:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:08:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:08:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:09:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:09:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:09:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:09:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:10:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:10:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:10:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:10:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:11:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:11:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:11:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:11:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:12:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:12:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:12:17","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:12:17","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:12:37","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:12:37","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:12:37","url":"/api/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:12:37","url":"/health","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:13:08","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:13:08","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:13:08","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:13:08","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:13:38","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:13:38","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:13:38","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:13:38","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:14:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:14:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:14:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:14:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:15:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:15:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:15:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:15:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:16:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:16:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:16:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:16:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:17:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:17:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:17:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:17:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:18:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:18:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:18:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:18:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:19:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:19:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:19:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:19:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:20:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:20:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:20:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:20:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:21:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:21:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:21:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:21:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:22:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:22:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:22:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:22:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭...","timestamp":"2025-08-02 21:22:46"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 21:22:47"}
{"level":"info","message":"HaoProxy服务器启动成功","timestamp":"2025-08-02 21:22:47"}
{"level":"info","message":"服务器地址: http://localhost:3000","timestamp":"2025-08-02 21:22:47"}
{"level":"info","message":"管理界面: http://localhost:3000","timestamp":"2025-08-02 21:22:47"}
{"level":"info","message":"API文档: http://localhost:3000/api","timestamp":"2025-08-02 21:22:47"}
{"level":"info","message":"代理端点: http://localhost:3000/proxy/*","timestamp":"2025-08-02 21:22:47"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:23:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:23:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:23:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:23:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:23:56","url":"/proxy/test","userAgent":"curl/8.7.1"}
{"headers":{"accept":"*/*","host":"localhost:3000","user-agent":"curl/8.7.1"},"ip":"::1","level":"info","message":"代理请求","method":"GET","originalUrl":"/proxy/test","path":"/","timestamp":"2025-08-02 21:23:56"}
{"duration":"188ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:23:57","url":"","userAgent":"curl/8.7.1"}
{"duration":"187ms","level":"info","message":"代理响应","method":"GET","originalUrl":"/proxy/test","statusCode":404,"timestamp":"2025-08-02 21:23:57"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:24:07","url":"/proxy/chat/completions","userAgent":"curl/8.7.1"}
{"headers":{"accept":"*/*","host":"localhost:3000","user-agent":"curl/8.7.1"},"ip":"::1","level":"info","message":"代理请求","method":"GET","originalUrl":"/proxy/chat/completions","path":"/","timestamp":"2025-08-02 21:24:07"}
{"duration":"96ms","ip":"::1","level":"error","message":"请求错误","method":"GET","statusCode":404,"timestamp":"2025-08-02 21:24:07","url":"","userAgent":"curl/8.7.1"}
{"duration":"96ms","level":"info","message":"代理响应","method":"GET","originalUrl":"/proxy/chat/completions","statusCode":404,"timestamp":"2025-08-02 21:24:07"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:24:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:24:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:24:17","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"0ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:24:17","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:06","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:25:06","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:06","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:25:06","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:09","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:25:09","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:10","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:10","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:25:10","url":"/css/style.css","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:25:10","url":"/js/app.js","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:10","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:25:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:25:10","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:10","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:25:10","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:10","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":304,"timestamp":"2025-08-02 21:25:10","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"POST","timestamp":"2025-08-02 21:25:14","url":"/api/proxies/openai-api/test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"118ms","ip":"::1","level":"info","message":"请求完成","method":"POST","statusCode":200,"timestamp":"2025-08-02 21:25:14","url":"/proxies/openai-api/test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:14","url":"/api/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:25:14","url":"/health","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 21:25:23","url":"/api/proxies/gemini-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理切换成功: Gemini API (gemini-api)","timestamp":"2025-08-02 21:25:23"}
{"duration":"4ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 21:25:23","url":"/proxies/gemini-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:23","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:25:23","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 21:25:23"}
{"level":"info","message":"HaoProxy服务器启动成功","timestamp":"2025-08-02 21:25:23"}
{"level":"info","message":"服务器地址: http://localhost:3000","timestamp":"2025-08-02 21:25:23"}
{"level":"info","message":"管理界面: http://localhost:3000","timestamp":"2025-08-02 21:25:23"}
{"level":"info","message":"API文档: http://localhost:3000/api","timestamp":"2025-08-02 21:25:23"}
{"level":"info","message":"代理端点: http://localhost:3000/proxy/*","timestamp":"2025-08-02 21:25:23"}
{"ip":"::1","level":"info","message":"请求开始","method":"PUT","timestamp":"2025-08-02 21:25:24","url":"/api/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"代理切换成功: Qwen3-Coder-480B-A35B-Instruct (openai-api)","timestamp":"2025-08-02 21:25:24"}
{"duration":"9ms","ip":"::1","level":"info","message":"请求完成","method":"PUT","statusCode":200,"timestamp":"2025-08-02 21:25:24","url":"/proxies/openai-api/toggle","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"请求开始","method":"GET","timestamp":"2025-08-02 21:25:24","url":"/api/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"请求完成","method":"GET","statusCode":200,"timestamp":"2025-08-02 21:25:24","url":"/proxies","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"服务器初始化完成","timestamp":"2025-08-02 21:25:25"}
{"level":"info","message":"HaoProxy服务器启动成功","timestamp":"2025-08-02 21:25:25"}
{"level":"info","message":"服务器地址: http://localhost:3000","timestamp":"2025-08-02 21:25:25"}
{"level":"info","message":"管理界面: http://localhost:3000","timestamp":"2025-08-02 21:25:25"}
{"level":"info","message":"API文档: http://localhost:3000/api","timestamp":"2025-08-02 21:25:25"}
{"level":"info","message":"代理端点: http://localhost:3000/proxy/*","timestamp":"2025-08-02 21:25:25"}
