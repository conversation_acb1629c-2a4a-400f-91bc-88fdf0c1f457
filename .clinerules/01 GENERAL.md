## 简要概述
此规则文件包含开发HaoProxy项目的指南，这是一个Node.js Web代理应用程序。这些规则涵盖了通信偏好、开发工作流程和项目特定的编码约定。

## 通信风格
- 使用简洁直接的沟通方式，避免不必要的客套话
- 专注于技术实现细节，而非冗长的解释
- 为架构和设计决策提供清晰的理由
- 使用中文(zh-CN)作为主要沟通语言

## 开发工作流程
- 遵循现有的项目结构，包括目录：config, logs, middleware, public, routes, utils
- 将中间件组件放置在middleware/目录中，并使用描述性名称
- 按功能组织routes/目录中的路由处理器
- 将工具函数放在utils/目录中以供共享功能使用
- 使用server.js作为主入口点
- 遵循middleware/errorHandler.js中的现有错误处理模式

## 编码最佳实践
- 使用一致的JavaScript/Node.js约定
- 遵循项目中的现有代码模式
- 使用英文的描述性变量和函数名称
- 在中间件中实现适当的错误处理
- 使用清晰的API端点结构化路由
- 使用async/await处理异步操作
- 遵循middleware/logger.js中的现有日志记录模式

## 项目上下文
- 这是一个Node.js Web代理应用程序(HaoProxy)
- 项目使用Express.js框架模式
- 静态文件从public/目录提供服务
- 配置管理通过config/目录处理
- 健康检查和代理处理工具在utils/目录中
- API路由在routes/api.js中组织
- 代理功能在routes/proxy.js中实现

## 其他指南
- 维护现有的文件结构和命名约定
- 使用.gitignore进行适当的文件排除
- 遵循现有的代码格式化和缩进模式
- 参考server.js、中间件文件和路由处理器以保持一致性
- 使用package.json进行依赖管理
